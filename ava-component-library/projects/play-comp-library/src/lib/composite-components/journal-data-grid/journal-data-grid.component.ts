import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataGridComponent } from '../../components/data-grid/data-grid.component';
import { AvaColumnDefDirective } from '../../components/data-grid/directive/ava-column-def.directive';
import { AvaHeaderCellDefDirective } from '../../components/data-grid/directive/ava-header-cell-def.directive';
import { AvaCellDefDirective } from '../../components/data-grid/directive/ava-cell-def.directive';
import { CheckboxComponent } from '../../components/checkbox/checkbox.component';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { IconComponent } from '../../components/icon/icon.component';

export interface JournalEntry {
  id: string;
  date: string;
  description: string;
  status: 'Template' | 'Posted' | 'Rejected' | 'Ready to Approve' | 'Draft';
  sourceTransaction: string;
  drCrTotals: string | null;
  hasDocuments: boolean;
  selected?: boolean;
}

export interface JournalSelectionEvent {
  selectedEntries: JournalEntry[];
  allSelected: boolean;
}

@Component({
  selector: 'ava-journal-data-grid',
  imports: [
    CommonModule,
    DataGridComponent,
    AvaColumnDefDirective,
    AvaHeaderCellDefDirective,
    AvaCellDefDirective,
    CheckboxComponent,
    AvaTagComponent,
    IconComponent
  ],
  templateUrl: './journal-data-grid.component.html',
  styleUrl: './journal-data-grid.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})
export class JournalDataGridComponent {
  @Input() journalEntries: JournalEntry[] = [];
  @Input() showSelection = true;
  @Input() showDocuments = true;
  @Input() enableSorting = true;
  @Input() enableFiltering = true;

  @Output() selectionChange = new EventEmitter<JournalSelectionEvent>();
  @Output() journalClick = new EventEmitter<JournalEntry>();
  @Output() documentClick = new EventEmitter<JournalEntry>();

  displayedColumns = ['select', 'journalId', 'date', 'description', 'status', 'sourceTransaction', 'drCrTotals', 'documents'];

  get selectedEntries(): JournalEntry[] {
    return this.journalEntries.filter(entry => entry.selected);
  }

  get allSelected(): boolean {
    return this.journalEntries.length > 0 && this.journalEntries.every(entry => entry.selected);
  }

  get someSelected(): boolean {
    return this.journalEntries.some(entry => entry.selected);
  }

  get indeterminate(): boolean {
    return this.someSelected && !this.allSelected;
  }

  toggleAll(checked: boolean): void {
    this.journalEntries.forEach(entry => entry.selected = checked);
    this.emitSelectionChange();
  }

  onSelectionChange(): void {
    this.emitSelectionChange();
  }

  private emitSelectionChange(): void {
    this.selectionChange.emit({
      selectedEntries: this.selectedEntries,
      allSelected: this.allSelected
    });
  }

  onJournalClick(entry: JournalEntry): void {
    this.journalClick.emit(entry);
  }

  onDocumentClick(entry: JournalEntry): void {
    this.documentClick.emit(entry);
  }

  getStatusTagColor(status: string): 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' {
    switch (status) {
      case 'Template':
        return 'warning';
      case 'Posted':
        return 'success';
      case 'Rejected':
        return 'error';
      case 'Ready to Approve':
        return 'primary';
      case 'Draft':
        return 'default';
      default:
        return 'default';
    }
  }

  formatDate(dateString: string): string {
    // Handle mm/dd/yyyy format or return as-is if already formatted
    if (dateString === 'mm/dd/yyyy') {
      return dateString;
    }
    
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  }

  formatCurrency(amount: string | null): string {
    if (!amount || amount === 'N/A') {
      return 'N/A';
    }
    
    // If already formatted with $, return as-is
    if (amount.startsWith('$')) {
      return amount;
    }
    
    // Format as currency
    const numericAmount = parseFloat(amount.replace(/[^0-9.-]/g, ''));
    if (isNaN(numericAmount)) {
      return amount;
    }
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(numericAmount);
  }
}
