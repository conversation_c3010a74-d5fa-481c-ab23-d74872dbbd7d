import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { JournalDataGridComponent, JournalEntry, JournalSelectionEvent } from '../../../../../play-comp-library/src/lib/composite-components/journal-data-grid/journal-data-grid.component';

@Component({
  selector: 'app-journal-data-grid',
  imports: [CommonModule, JournalDataGridComponent],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent {
  
  journalData: JournalEntry[] = [
    {
      id: '388236',
      date: '01/15/2024',
      description: 'This is a template for mortgage payments',
      status: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: null,
      hasDocuments: true,
      selected: false
    },
    {
      id: '388237',
      date: '01/14/2024',
      description: 'Monthly rent collection journal entry',
      status: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '15000.00',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388238',
      date: '01/13/2024',
      description: 'Utility expense allocation - rejected due to missing receipts',
      status: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: null,
      hasDocuments: false,
      selected: false
    },
    {
      id: '388239',
      date: '01/12/2024',
      description: 'Property tax adjustment entry',
      status: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: '8500.00',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388240',
      date: '01/11/2024',
      description: 'Insurance premium allocation template',
      status: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: null,
      hasDocuments: true,
      selected: false
    },
    {
      id: '388241',
      date: '01/10/2024',
      description: 'Maintenance expense journal - awaiting approval',
      status: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: '3250.75',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388242',
      date: '01/09/2024',
      description: 'Vendor payment reversal - incorrect amount',
      status: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '204893.00',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388243',
      date: '01/08/2024',
      description: 'Depreciation calculation - work in progress',
      status: 'Draft',
      sourceTransaction: 'Automated',
      drCrTotals: '12500.00',
      hasDocuments: false,
      selected: false
    },
    {
      id: '388244',
      date: '01/07/2024',
      description: 'Accrued interest calculation draft',
      status: 'Draft',
      sourceTransaction: 'Automated',
      drCrTotals: '1875.50',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388245',
      date: '01/06/2024',
      description: 'Monthly payroll journal entry',
      status: 'Posted',
      sourceTransaction: 'Automated',
      drCrTotals: '45000.00',
      hasDocuments: true,
      selected: false
    },
    {
      id: '388246',
      date: '01/05/2024',
      description: 'Bank reconciliation adjustments',
      status: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '750.25',
      hasDocuments: true,
      selected: false
    }
  ];

  selectedCount = 0;

  onSelectionChange(event: JournalSelectionEvent): void {
    this.selectedCount = event.selectedEntries.length;
    console.log('Selection changed:', {
      selectedCount: event.selectedEntries.length,
      allSelected: event.allSelected,
      selectedEntries: event.selectedEntries
    });
  }

  onJournalClick(entry: JournalEntry): void {
    console.log('Journal clicked:', entry);
    alert(`Clicked on Journal ID: ${entry.id}`);
  }

  onDocumentClick(entry: JournalEntry): void {
    console.log('Document clicked for journal:', entry);
    alert(`View documents for Journal ID: ${entry.id}`);
  }
}
