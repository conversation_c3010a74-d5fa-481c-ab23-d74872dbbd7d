<div class="demo-page">
  <!-- Header -->
  <div class="demo-header">
    <div class="container">
      <h1>Journal Data Grid</h1>
      <p class="demo-description">
        A comprehensive data grid component specifically designed for journal
        entries with selection, status indicators, and document management
        capabilities.
      </p>
    </div>
  </div>

  <!-- Demo Content -->
  <div class="demo-content">
    <div class="container">
      <!-- Journal Data Grid Demo -->
      <div class="demo-section">
        <div class="section-header">
          <h2>Journal Entries</h2>
          <p>
            Interactive journal data grid with selection, sorting, and filtering
            capabilities
          </p>
        </div>

        <div class="grid-container">
          <ava-journal-data-grid
            [journalEntries]="journalData"
            [showSelection]="true"
            [showDocuments]="true"
            [enableSorting]="true"
            [enableFiltering]="true"
            (selectionChange)="onSelectionChange($event)"
            (journalClick)="onJournalClick($event)"
            (documentClick)="onDocumentClick($event)"
          >
          </ava-journal-data-grid>
        </div>
      </div>
    </div>
  </div>
</div>
