<div class="ava-journal-data-grid">
  <ava-data-grid 
    [dataSource]="journalEntries" 
    [displayedColumns]="displayedColumns"
    class="journal-grid">
    
    <!-- Selection Column -->
    <ng-container avaColumnDef="select" *ngIf="showSelection">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell selection-header">
          <ava-checkbox
            [isChecked]="allSelected"
            [indeterminate]="indeterminate"
            (isCheckedChange)="toggleAll($event)"
            size="small">
          </ava-checkbox>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell selection-cell">
          <ava-checkbox
            [isChecked]="entry.selected || false"
            (isCheckedChange)="entry.selected = $event; onSelectionChange()"
            size="small">
          </ava-checkbox>
        </div>
      </ng-container>
    </ng-container>

    <!-- Journal ID Column -->
    <ng-container avaColumnDef="journalId" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Journal ID</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell journal-id-cell">
          <a 
            href="javascript:void(0)" 
            class="journal-link"
            (click)="onJournalClick(entry)"
            [attr.aria-label]="'View journal ' + entry.id">
            {{ entry.id }}
          </a>
        </div>
      </ng-container>
    </ng-container>

    <!-- Date Column -->
    <ng-container avaColumnDef="date" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Date</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell date-cell">
          <span class="date-text">{{ formatDate(entry.date) }}</span>
        </div>
      </ng-container>
    </ng-container>

    <!-- Journal Description Column -->
    <ng-container avaColumnDef="description" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Journal Description</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell description-cell">
          <span class="description-text" [title]="entry.description">{{ entry.description }}</span>
        </div>
      </ng-container>
    </ng-container>

    <!-- Journal Status Column -->
    <ng-container avaColumnDef="status" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Journal Status</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell status-cell">
          <ava-tag
            [label]="entry.status"
            [color]="getStatusTagColor(entry.status)"
            size="sm"
            [pill]="true">
          </ava-tag>
        </div>
      </ng-container>
    </ng-container>

    <!-- Source Transaction Column -->
    <ng-container avaColumnDef="sourceTransaction" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Source Transaction</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell source-transaction-cell">
          <span class="source-text">{{ entry.sourceTransaction }}</span>
        </div>
      </ng-container>
    </ng-container>

    <!-- Dr/Cr Totals Column -->
    <ng-container avaColumnDef="drCrTotals" [sortable]="enableSorting" [filter]="enableFiltering">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Dr/Cr Totals</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell totals-cell">
          <span class="totals-text" [class.na-text]="entry.drCrTotals === 'N/A' || !entry.drCrTotals">
            {{ formatCurrency(entry.drCrTotals) }}
          </span>
        </div>
      </ng-container>
    </ng-container>

    <!-- Documents Column -->
    <ng-container avaColumnDef="documents" *ngIf="showDocuments">
      <ng-container *avaHeaderCellDef>
        <div class="header-cell">
          <span class="header-text">Documents</span>
        </div>
      </ng-container>
      <ng-container *avaCellDef="let entry">
        <div class="data-cell documents-cell">
          <button 
            *ngIf="entry.hasDocuments"
            class="document-button"
            (click)="onDocumentClick(entry)"
            [attr.aria-label]="'View documents for journal ' + entry.id"
            type="button">
            <ava-icon 
              iconName="file-text" 
              iconSize="16" 
              iconColor="var(--color-text-secondary)">
            </ava-icon>
          </button>
          <span *ngIf="!entry.hasDocuments" class="no-documents" aria-label="No documents available">-</span>
        </div>
      </ng-container>
    </ng-container>

  </ava-data-grid>
</div>
