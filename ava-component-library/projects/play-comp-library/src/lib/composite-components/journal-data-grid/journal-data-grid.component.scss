// Using CSS custom properties from tokens

.ava-journal-data-grid {
  width: 100%;
  
  .journal-grid {
    .ava-data-table {
      th, td {
        vertical-align: middle;
      }
      
      // Header styling
      .header-cell {
        display: flex;
        align-items: center;
        min-height: 40px;

        .header-text {
          font-weight: 600;
          color: var(--color-text-primary, #1f2937);
          font-size: var(--font-size-sm, 0.875rem);
        }
      }
      
      // Data cell styling
      .data-cell {
        display: flex;
        align-items: center;
        min-height: 48px;
        padding: 8px 0;
      }
      
      // Selection column
      .selection-header,
      .selection-cell {
        width: 48px;
        min-width: 48px;
        max-width: 48px;
        justify-content: center;
      }
      
      // Journal ID column
      .journal-id-cell {
        min-width: 100px;
        
        .journal-link {
          color: var(--color-text-brand, #3b82f6);
          text-decoration: none;
          font-weight: 500;
          transition: color 0.2s ease;

          &:hover {
            color: var(--color-text-brand-hover, #2563eb);
            text-decoration: underline;
          }

          &:focus {
            outline: 2px solid var(--color-border-focus, #3b82f6);
            outline-offset: 2px;
            border-radius: 2px;
          }
        }
      }
      
      // Date column
      .date-cell {
        min-width: 120px;
        
        .date-text {
          color: var(--color-text-secondary, #6b7280);
          font-size: var(--font-size-sm, 0.875rem);
        }
      }
      
      // Description column
      .description-cell {
        min-width: 200px;
        max-width: 300px;
        
        .description-text {
          color: var(--color-text-primary);
          font-size: var(--font-size-sm);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 100%;
        }
      }
      
      // Status column
      .status-cell {
        min-width: 140px;
        
        ava-tag {
          .ava-tag {
            font-size: var(--font-size-xs);
            font-weight: 500;
          }
        }
      }
      
      // Source transaction column
      .source-transaction-cell {
        min-width: 120px;
        
        .source-text {
          color: var(--color-text-secondary);
          font-size: var(--font-size-sm);
        }
      }
      
      // Totals column
      .totals-cell {
        min-width: 120px;
        text-align: right;
        justify-content: flex-end;
        
        .totals-text {
          color: var(--color-text-primary);
          font-size: var(--font-size-sm);
          font-weight: 500;
          
          &.na-text {
            color: var(--color-text-tertiary);
            font-weight: 400;
          }
        }
      }
      
      // Documents column
      .documents-cell {
        width: 80px;
        min-width: 80px;
        max-width: 80px;
        justify-content: center;
        
        .document-button {
          background: none;
          border: none;
          padding: 8px;
          border-radius: 4px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background-color 0.2s ease;
          
          &:hover {
            background-color: var(--color-background-secondary);
          }
          
          &:focus {
            outline: 2px solid var(--color-border-focus);
            outline-offset: 2px;
          }
          
          &:active {
            background-color: var(--color-background-tertiary);
          }
        }
        
        .no-documents {
          color: var(--color-text-tertiary);
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .ava-journal-data-grid {
    .journal-grid {
      .ava-data-table {
        .description-cell {
          max-width: 150px;
        }
        
        .source-transaction-cell,
        .totals-cell {
          min-width: 100px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ava-journal-data-grid {
    .journal-grid {
      .ava-data-table {
        font-size: var(--font-size-xs);
        
        .data-cell {
          min-height: 40px;
          padding: 4px 0;
        }
        
        .description-cell {
          max-width: 120px;
        }
      }
    }
  }
}
