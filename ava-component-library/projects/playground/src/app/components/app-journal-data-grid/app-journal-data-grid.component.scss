// Using CSS custom properties from tokens

.demo-page {
  min-height: 100vh;
  background: var(--color-background-primary);
}

.demo-header {
  background: linear-gradient(135deg, var(--color-background-brand) 0%, var(--color-background-brand-secondary, var(--color-background-brand)) 100%);
  color: var(--color-text-on-brand);
  padding: 4rem 0 3rem;
  text-align: center;

  h1 {
    font-size: 3rem;
    font-weight: 700;
    margin: 0 0 1rem;
    letter-spacing: -0.02em;
  }

  .demo-description {
    font-size: 1.25rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.demo-content {
  padding: 3rem 0;
}

.selection-summary {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  
  .summary-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .selection-count {
    font-weight: 600;
    color: var(--color-text-primary);
  }
  
  .selection-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }
  
  .action-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &.primary {
      background: var(--color-background-success);
      color: var(--color-text-on-brand);
      
      &:hover {
        background: var(--color-background-success-hover, var(--color-background-success));
      }
    }
    
    &.secondary {
      background: var(--color-background-error);
      color: var(--color-text-on-brand);
      
      &:hover {
        background: var(--color-background-error-hover, var(--color-background-error));
      }
    }
    
    &.tertiary {
      background: var(--color-background-tertiary);
      color: var(--color-text-primary);
      border: 1px solid var(--color-border-primary);
      
      &:hover {
        background: var(--color-background-quaternary);
      }
    }
  }
}

.demo-section {
  margin-bottom: 4rem;
  
  .section-header {
    margin-bottom: 2rem;
    
    h2 {
      font-size: 2rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin: 0 0 0.5rem;
    }
    
    p {
      color: var(--color-text-secondary);
      font-size: 1.125rem;
      margin: 0;
    }
  }
  
  .grid-container {
    background: var(--color-background-primary);
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    overflow: hidden;
  }
}

.features-section {
  margin: 4rem 0;
  
  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0 0 2rem;
    text-align: center;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }
  
  .feature-card {
    background: var(--color-background-secondary);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    border: 1px solid var(--color-border-primary);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
    }
    
    .feature-icon {
      margin-bottom: 1.5rem;
      
      .icon-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--color-background-brand);
        color: var(--color-text-on-brand);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin: 0 auto;
      }
    }
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary);
      margin: 0 0 1rem;
    }
    
    p {
      color: var(--color-text-secondary);
      line-height: 1.6;
      margin: 0;
    }
  }
}

.code-section {
  margin: 4rem 0;
  
  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 0 0 1.5rem;
  }
  
  .code-block {
    background: var(--color-background-tertiary);
    border: 1px solid var(--color-border-primary);
    border-radius: 8px;
    overflow-x: auto;
    
    pre {
      margin: 0;
      padding: 1.5rem;
      
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.875rem;
        line-height: 1.6;
        color: var(--color-text-primary);
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-header {
    padding: 3rem 0 2rem;
    
    h1 {
      font-size: 2.5rem;
    }
    
    .demo-description {
      font-size: 1.125rem;
    }
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .selection-summary {
    .summary-content {
      flex-direction: column;
      align-items: stretch;
    }
    
    .selection-actions {
      justify-content: center;
    }
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
